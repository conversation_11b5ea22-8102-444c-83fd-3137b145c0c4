// lib/i18n/en/pregnancy.ts

export const pregnancy = {
    'pregnancy.insights.title': 'Weekly AI Insights',
    'pregnancy.insights.description': 'Information on fetal development and health by week.',
    'pregnancy.insights.chat': 'Chat with AI',
    'pregnancy.insights.more': 'More Insights',
    'pregnancy.progress.title': 'Pregnancy Progress',
    'pregnancy.progress.description': 'Key progress summary',
    'pregnancy.checklist.title': 'Weekly Pregnancy Checklist',
    'pregnancy.checklist.description': 'Trimester {trimester}: Check off these important tasks.',
    'pregnancy.checklist.trimester1.todo1': 'Schedule your first prenatal visit',
    'pregnancy.checklist.trimester1.todo2': 'Start taking folic acid supplements',
    'pregnancy.checklist.trimester1.todo3': 'Share the news (family, work)',
    'pregnancy.checklist.trimester2.todo1': 'Schedule detailed ultrasound & glucose screening',
    'pregnancy.checklist.trimester2.todo2': 'Shop for maternity clothes',
    'pregnancy.checklist.trimester2.todo3': 'Start thinking about baby names',
    'pregnancy.checklist.trimester3.todo1': 'Pack your hospital bag',
    'pregnancy.checklist.trimester3.todo2': 'Plan your maternity leave',
    'pregnancy.checklist.trimester3.todo3': 'Set up the nursery',
    'pregnancy.weightChart.title': 'Weight Change',
    'pregnancy.weightChart.description': 'Track your weekly weight changes to manage a healthy pregnancy.',
    'pregnancy.weightChart.myWeight': 'My Weight',
    'pregnancy.weightChart.myWeightKg': 'My Weight (kg)',
    'pregnancy.weightChart.recommendedRange': 'Recommended Weight Range',
    'pregnancy.weightChart.recommendedMin': 'Recommended Min',
    'pregnancy.bloodPressureChart.title': 'Blood Pressure Change',
    'pregnancy.bloodPressureChart.description': 'Regularly check your blood pressure to monitor your health during pregnancy.',
    'pregnancy.bloodPressureChart.systolic': 'Systolic',
    'pregnancy.bloodPressureChart.systolicMmhg': 'Systolic (mmHg)',
    'pregnancy.bloodPressureChart.diastolic': 'Diastolic',
    'pregnancy.bloodPressureChart.diastolicMmhg': 'Diastolic (mmHg)',
    'pregnancy.insights.weeks.1-4.1': "This is early pregnancy. Start taking folic acid.",
    'pregnancy.insights.weeks.1-4.2': "Listen to your body's changes.",
    'pregnancy.insights.weeks.5-8.1': "Morning sickness may begin. Eat small, frequent meals.",
    'pregnancy.insights.weeks.5-8.2': "It's time to schedule your first prenatal visit.",
    'pregnancy.insights.weeks.9-13.1': "The baby is growing rapidly. You can hear the fetal heartbeat.",
    'pregnancy.insights.weeks.9-13.2': "You may feel very tired, so get plenty of rest.",
    'pregnancy.insights.weeks.14-17.1': "You've entered the second trimester. Your energy will gradually return.",
    'pregnancy.insights.weeks.14-17.2': "Now that you're in a stable period, try starting some light exercise.",
    'pregnancy.insights.weeks.18-22.1': "You might be able to feel fetal movement!",
    'pregnancy.insights.weeks.18-22.2': "You can check the baby's health with a detailed ultrasound.",
    'pregnancy.insights.weeks.23-26.1': "The baby's hearing is developing. Try talking to your baby.",
    'pregnancy.insights.weeks.23-26.2': "Your belly is starting to show more, so wear comfortable clothes.",
    'pregnancy.insights.weeks.27-30.1': "The third trimester has begun. Start gradually preparing for birth.",
    'pregnancy.insights.weeks.27-30.2': "Check for gestational diabetes or high blood pressure with regular check-ups.",
    'pregnancy.insights.weeks.31-35.1': "The baby is almost fully grown. It's a good idea to pack your hospital bag in advance.",
    'pregnancy.insights.weeks.31-35.2': "You may feel short of breath, so avoid strenuous activities.",
    'pregnancy.insights.weeks.36-40.1': "Labor is imminent. Learn to distinguish between Braxton Hicks contractions and true labor.",
    'pregnancy.insights.weeks.36-40.2': "Be ready to go to the hospital at any time.",
  };