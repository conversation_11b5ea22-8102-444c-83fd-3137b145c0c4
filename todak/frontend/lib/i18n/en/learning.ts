// lib/i18n/en/learning.ts

export const learning = {
  'learning.planning.title': 'Planning Resources',
  'learning.planning.description': 'Information to help you prepare for conception and a healthy pregnancy.',
  'learning.planning.item1.title': 'Nutrition & Supplements',
  'learning.planning.item1.description': 'Learn about essential nutrients for a healthy pregnancy.',
  'learning.planning.item2.title': 'Fertility & Ovulation',
  'learning.planning.item2.description': 'Understand your fertile window to improve conception chances.',
  'learning.pregnancy.title': 'Pregnancy Resources',
  'learning.pregnancy.description': 'Information for tracking health and fetal development during pregnancy.',
  'learning.pregnancy.item1.title': 'Weekly Fetal Development',
  'learning.pregnancy.item1.description': 'See how your baby is growing each week.',
  'learning.pregnancy.item2.title': 'Preparing for Birth',
  'learning.pregnancy.item2.description': 'Learn about the labor process and pain relief options.',
  'learning.newborn.title': 'Newborn Care Resources',
  'learning.newborn.description': 'Essential information for caring for your newborn.',
  'learning.newborn.item1.title': 'Feeding Your Baby',
  'learning.newborn.item1.description': 'Guidance on breastfeeding and formula feeding.',
  'learning.newborn.item2.title': 'Sleep Patterns & Safety',
  'learning.newborn.item2.description': 'Establish healthy sleep habits and ensure a safe environment.',
};
