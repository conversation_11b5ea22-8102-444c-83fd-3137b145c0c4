// lib/i18n/en/onboarding.ts

export const onboarding = {
    'onboarding.welcome.title': 'Welcome to Maternal',
    'onboarding.welcome.subtitle': 'Your AI companion for motherhood.',
    'onboarding.welcome.description': 'From planning to pregnancy and newborn care, we provide personalized support at every step.',
    'onboarding.modes.title': 'Choose Your Current Stage',
    'onboarding.modes.subtitle': 'We’ll personalize your experience based on your selection.',
    'onboarding.modes.planning.title': 'Planning Pregnancy',
    'onboarding.modes.planning.description': 'Preparing for conception and a healthy pregnancy.',
    'onboarding.modes.pregnancy.title': 'During Pregnancy',
    'onboarding.modes.pregnancy.description': 'Tracking development, health, and preparing for birth.',
    'onboarding.modes.newborn.title': 'Newborn Care',
    'onboarding.modes.newborn.description': 'Logging activities and monitoring your baby’s growth.',
    'onboarding.features.title': 'Key Features',
    'onboarding.features.subtitle': 'Your journey is unique. Our personalized AI provides tailored support.',
    'onboarding.features.ai.title': 'AI-Powered Chat & Insights',
    'onboarding.features.ai.description': 'Get instant, reliable answers to your questions, 24/7.',
    'onboarding.features.tracking.title': 'Comprehensive Health Logging',
    'onboarding.features.tracking.description': 'Track symptoms, appointments, and milestones with ease.',
    'onboarding.features.insights.title': 'Personalized Patterns & Trends',
    'onboarding.features.insights.description': 'Understand your body and your baby’s development better.',
    'onboarding.ready.title': 'You’re All Set!',
    'onboarding.ready.subtitle': 'Your personalized journey awaits.',
    'onboarding.ready.description': 'Let’s start this wonderful chapter of your life together.',
    'onboarding.buttons.skip': 'Skip',
    'onboarding.buttons.getStarted': 'Get Started',
    'onboarding.buttons.next': 'Next',
    'signup.title': 'Welcome! Let\'s Get Started',
    'signup.subtitle': 'Create your account to begin your journey',
    'signup.name': 'Name or Nickname',
    'signup.email': 'Email Address',
    'signup.terms': 'I agree to the Terms of Service',
    'signup.privacy': 'I agree to the Privacy Policy',
    'signup.button': 'Start My Journey',
    'signup.selectedMode': 'Selected Mode',
  };
  