
import { common as enCommon } from './en/common';
import { dashboard as enDashboard } from './en/dashboard';
import { learning as enLearning } from './en/learning';
import { newborn as enNewborn } from './en/newborn';
import { onboarding as enOnboarding } from './en/onboarding';
import { planning as enPlanning } from './en/planning';
import { pregnancy as enPregnancy } from './en/pregnancy';
import { pricing as enPricing } from './en/pricing';
import { profile as enProfile } from './en/profile';

import { common as koCommon } from './ko/common';
import { dashboard as koDashboard } from './ko/dashboard';
import { learning as koLearning } from './ko/learning';
import { newborn as koNewborn } from './ko/newborn';
import { onboarding as koOnboarding } from './ko/onboarding';
import { planning as koPlanning } from './ko/planning';
import { pregnancy as koPregnancy } from './ko/pregnancy';
import { pricing as koPricing } from './ko/pricing';
import { profile as koProfile } from './ko/profile';

const enTranslations = {
  ...enCommon,
  ...enDashboard,
  ...enLearning,
  ...enNewborn,
  ...enOnboarding,
  ...enPlanning,
  ...enPregnancy,
  ...enPricing,
  ...enProfile,
};

const koTranslations = {
  ...koCommon,
  ...koDashboard,
  ...koLearning,
  ...koNewborn,
  ...koOnboarding,
  ...koPlanning,
  ...koPregnancy,
  ...koPricing,
  ...koProfile,
};

export const resources = {
  en: {
    translation: enTranslations,
  },
  ko: {
    translation: koTranslations,
  },
};