// screens/HomeScreen.tsx
import React from 'react';
import { Text } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAppLogic } from '@/hooks/useAppLogic';
import ModeSelector from '@/components/ModeSelector';
import OnboardingFlow from '@/components/OnboardingFlow';
import SplashScreen from '@/components/SplashScreen';
// import SignupScreen from '@/components/SignupScreen';
// import DefaultLayout from '@/components/layouts/DefaultLayout';
// import NewbornLayout from '@/components/layouts/NewbornLayout';

const HomeScreen = () => {
  const {
    showSplash,
    currentMode,
    setCurrentMode,
    showOnboarding,
    showSignup,
    currentLanguage,
    setCurrentLanguage,
    selectedDate,
    t,
    handleOnboardingComplete,
    handleSignupComplete,
    toggleLanguage,
  } = useAppLogic();

  if (showSplash) return <SplashScreen />;

  if (showOnboarding) {
    return (
      <OnboardingFlow
        onComplete={handleOnboardingComplete}
        language={currentLanguage}
        toggleLanguage={toggleLanguage}
        t={t}
      />
    );
  }

  return (
    <SafeAreaView className="flex-1 items-center justify-center bg-white">
      <Text className="text-xl font-bold text-blue-500">
        Welcome to NativeWind!
      </Text>
      {/* 모드 선택 등 추가 UI는 여기서 계속 확장 가능 */}
    </SafeAreaView>
  );
};

export default HomeScreen;
