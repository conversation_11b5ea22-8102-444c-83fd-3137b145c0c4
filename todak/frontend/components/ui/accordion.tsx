// components/ui/accordion.tsx
import React, { useState } from 'react';
import { View, Text, TouchableOpacity, LayoutAnimation, Platform, UIManager } from 'react-native';
import { ChevronDown } from 'lucide-react-native';

if (Platform.OS === 'android') {
  UIManager.setLayoutAnimationEnabledExperimental?.(true);
}

const accordion = ({
  title,
  children,
  defaultOpen = false,
}: {
  title: string;
  children: React.ReactNode;
  defaultOpen?: boolean;
}) => {
  const [expanded, setExpanded] = useState(defaultOpen);

  const toggleExpand = () => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setExpanded(!expanded);
  };

  return (
    <View className="border-b border-border px-4">
      <TouchableOpacity
        className="flex-row justify-between items-center py-4"
        onPress={toggleExpand}
        activeOpacity={0.7}
      >
        <Text className="font-semibold text-base text-foreground">{title}</Text>
        <ChevronDown
          className={`text-muted-foreground transition-transform duration-200 ${
            expanded ? 'rotate-180' : ''
          }`}
          size={20}
        />
      </TouchableOpacity>

      {expanded && (
        <View className="pt-2 pb-4 px-1">
          {children}
        </View>
      )}
    </View>
  );
};

export default accordion;
