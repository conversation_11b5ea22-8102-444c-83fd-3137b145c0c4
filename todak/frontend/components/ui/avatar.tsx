// components/ui/avatar.tsx
import React from 'react';
import { Image, Text, View } from 'react-native';
import { twMerge } from 'tailwind-merge';

type AvatarProps = {
  source?: { uri: string };
  fallback?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
};

const sizeClasses = {
  sm: 'w-8 h-8 text-sm',
  md: 'w-10 h-10 text-base',
  lg: 'w-14 h-14 text-lg',
};

const Avatar: React.FC<AvatarProps> = ({
  source,
  fallback = '?',
  size = 'md',
  className = '',
}) => {
  const sizeStyle = sizeClasses[size];

  return (
    <View
      className={twMerge(
        `rounded-full bg-muted justify-center items-center overflow-hidden ${sizeStyle}`,
        className
      )}
    >
      {source?.uri ? (
        <Image
          source={source}
          className="w-full h-full"
          resizeMode="cover"
        />
      ) : (
        <Text className="text-white font-semibold">{fallback}</Text>
      )}
    </View>
  );
};

export default Avatar;
