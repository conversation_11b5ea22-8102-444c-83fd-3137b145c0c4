// components/ui/aspect-ratio.tsx
import React, { useMemo } from 'react';
import { View, StyleSheet } from 'react-native';

type AspectRatioProps = {
  ratio?: number; // 예: 16 / 9
  children: React.ReactNode;
  style?: any;
};

const AspectRatio = ({ ratio = 1, children, style }: AspectRatioProps) => {
  const aspectStyle = useMemo(() => ({
    aspectRatio: ratio,
  }), [ratio]);

  return (
    <View style={[styles.container, aspectStyle, style]}>
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    overflow: 'hidden',
  },
});

export default AspectRatio;
