// components/ui/alert.tsx
import React from 'react';
import { View, Text } from 'react-native';
import { cn } from '@/lib/utils'; // 클래스 병합 도우미

type AlertProps = {
  variant?: 'default' | 'destructive';
  children: React.ReactNode;
  className?: string;
};

export const alert = ({ variant = 'default', children, className }: AlertProps) => {
  const baseClass =
    'w-full rounded-xl border p-4 flex-row bg-background text-foreground';
  const destructiveClass = 'border-red-500 bg-red-50 text-red-800';

  const finalClass = cn(
    baseClass,
    variant === 'destructive' ? destructiveClass : '',
    className
  );

  return <View className={finalClass}>{children}</View>;
};

type AlertTitleProps = {
  children: React.ReactNode;
  className?: string;
};

export const AlertTitle = ({ children, className }: AlertTitleProps) => {
  return (
    <Text className={cn('text-base font-bold text-foreground mb-1', className)}>
      {children}
    </Text>
  );
};

type AlertDescriptionProps = {
  children: React.ReactNode;
  className?: string;
};

export const AlertDescription = ({ children, className }: AlertDescriptionProps) => {
  return (
    <Text className={cn('text-sm text-muted-foreground', className)}>
      {children}
    </Text>
  );
};
