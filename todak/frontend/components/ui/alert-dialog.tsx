// components/ui/alert-dialog.tsx
import React, { useEffect, useRef } from 'react';
import {
  Modal,
  View,
  Text,
  Pressable,
  Animated,
  Easing,
  TouchableOpacity,
} from 'react-native';
import { X } from 'lucide-react-native';
import { cn } from '@/lib/utils';

type AlertDialogProps = {
  visible: boolean;
  onConfirm: () => void;
  onCancel: () => void;
  title: string;
  description?: string;
  confirmText?: string;
  cancelText?: string;
  variant?: 'primary' | 'outline' | 'destructive';
  showDismiss?: boolean;
};

const AlertDialog = ({
  visible,
  onConfirm,
  onCancel,
  title,
  description,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  variant = 'primary',
  showDismiss = true,
}: AlertDialogProps) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: visible ? 1 : 0,
      duration: 200,
      easing: Easing.ease,
      useNativeDriver: true,
    }).start();
  }, [visible]);

  const getButtonStyle = (type: 'confirm' | 'cancel') => {
    const base = 'px-4 py-2 rounded-xl';
    if (type === 'cancel') return `${base} border border-border bg-secondary`;
    if (variant === 'destructive') return `${base} bg-red-500`;
    if (variant === 'outline') return `${base} border border-primary`;
    return `${base} bg-primary`;
  };

  const getTextStyle = (type: 'confirm' | 'cancel') => {
    if (type === 'cancel') return 'text-sm text-foreground';
    if (variant === 'destructive') return 'text-sm text-white';
    if (variant === 'outline') return 'text-sm text-primary';
    return 'text-sm text-white';
  };

  return (
    <Modal
      transparent
      visible={visible}
      animationType="none"
      onRequestClose={onCancel}
    >
      <Animated.View
        style={{ opacity: fadeAnim }}
        className="flex-1 bg-black/60 justify-center items-center px-6"
      >
        <View className="w-full max-w-md bg-white dark:bg-zinc-900 p-6 rounded-2xl shadow-lg space-y-4 relative">
          {/* Dismiss (X) Button */}
          {showDismiss && (
            <TouchableOpacity
              onPress={onCancel}
              className="absolute right-4 top-4 z-10"
            >
              <X size={20} color="#666" />
            </TouchableOpacity>
          )}

          {/* Title */}
          <Text className="text-lg font-bold text-center text-foreground">
            {title}
          </Text>

          {/* Description */}
          {description && (
            <Text className="text-sm text-muted-foreground text-center">
              {description}
            </Text>
          )}

          {/* Footer Buttons */}
          <View className="flex-row justify-end space-x-2 pt-2">
            <Pressable
              className={getButtonStyle('cancel')}
              onPress={onCancel}
            >
              <Text className={getTextStyle('cancel')}>{cancelText}</Text>
            </Pressable>
            <Pressable
              className={getButtonStyle('confirm')}
              onPress={onConfirm}
            >
              <Text className={getTextStyle('confirm')}>{confirmText}</Text>
            </Pressable>
          </View>
        </View>
      </Animated.View>
    </Modal>
  );
};

export default AlertDialog;
