// components/ModeSelector.tsx
import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import clsx from 'clsx';
import { CheckCircle } from 'lucide-react-native';

interface ModeSelectorProps {
  currentMode: string;
  onModeChange: (mode: string) => void;
  t: (key: string) => string;
}

const ModeSelector: React.FC<ModeSelectorProps> = ({ currentMode, onModeChange, t }) => {
  const modes = [
    {
      id: 'planning',
      title: t('modes.planning'),
      description: t('onboarding.modes.planning.description'),
      icon: '🤰',
    },
    {
      id: 'pregnancy',
      title: t('modes.pregnancy'),
      description: t('onboarding.modes.pregnancy.description'),
      icon: '🤱',
    },
    {
      id: 'newborn',
      title: t('modes.newborn'),
      description: t('onboarding.modes.newborn.description'),
      icon: '👶',
    }
  ];

  return (
    <View className="bg-white rounded-xl p-6 shadow space-y-4">
      <Text className="text-xl font-semibold mb-2">{t('settings.careMode')}</Text>
      <View className="space-y-4">
        {modes.map((mode) => (
          <TouchableOpacity
            key={mode.id}
            onPress={() => onModeChange(mode.id)}
            className={clsx(
              'p-4 rounded-lg border flex-row items-start space-x-3',
              currentMode === mode.id ? 'bg-primary border-primary' : 'bg-muted border-muted'
            )}
          >
            <Text className="text-2xl">{mode.icon}</Text>
            <View className="flex-1">
              <Text className="font-medium text-foreground">{mode.title}</Text>
              <Text
                className={clsx(
                  'text-xs',
                  currentMode === mode.id ? 'text-primary-foreground/80' : 'text-muted-foreground'
                )}
              >
                {mode.description}
              </Text>
            </View>
            {currentMode === mode.id && (
              <View className="px-2 py-1 rounded-full bg-primary-foreground/20">
                <Text className="text-primary-foreground text-xs">{t('modes.active')}</Text>
              </View>
            )}
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

export default ModeSelector;
