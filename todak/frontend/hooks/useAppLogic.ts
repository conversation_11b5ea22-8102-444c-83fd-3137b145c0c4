import { useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useTranslation } from './useTranslation';

export type Mode = 'planning' | 'pregnancy' | 'newborn';

export const useAppLogic = () => {
  const [showSplash, setShowSplash] = useState(true);
  const [currentMode, setCurrentMode] = useState<Mode>('planning');
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [showSignup, setShowSignup] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>();
  const { t, currentLanguage, toggleLanguage, setCurrentLanguage } = useTranslation();

  // 개발 중 테스트를 위해 AsyncStorage 초기화 (실제 배포 시 제거)
  useEffect(() => {
    const resetStorage = async () => {
      await AsyncStorage.removeItem('onboardingCompleted');
      console.log('Storage reset for testing');
    };
    resetStorage();
  }, []);

  useEffect(() => {
    const timer = setTimeout(() => setShowSplash(false), 1500);

    (async () => {
      try {
        const onboardingCompleted = await AsyncStorage.getItem('onboardingCompleted');
        console.log('onboardingCompleted:', onboardingCompleted); // 디버깅용
        if (!onboardingCompleted) {
          setShowOnboarding(true);
        } else {
          setShowOnboarding(false);

          const storedMode = await AsyncStorage.getItem('currentMode');
          if (storedMode) setCurrentMode(storedMode as Mode);

          const storedDate = await AsyncStorage.getItem('selectedDate');
          if (storedDate) setSelectedDate(new Date(storedDate));
        }
      } catch (error) {
        console.error('Error checking onboarding status:', error);
        setShowOnboarding(true); // 에러 발생 시 기본값으로 onboarding 표시
      }
    })();

    return () => clearTimeout(timer);
  }, []);

  const handleOnboardingComplete = async (mode: Mode, date?: Date) => {
    await AsyncStorage.setItem('onboardingCompleted', 'true');
    await AsyncStorage.setItem('currentMode', mode);
    if (date) await AsyncStorage.setItem('selectedDate', date.toISOString());
    setShowOnboarding(false);
    setShowSignup(true);
    setCurrentMode(mode);
    setSelectedDate(date);
  };

  const handleSignupComplete = () => {
    setShowSignup(false);
  };

  return {
    showSplash,
    currentMode,
    setCurrentMode,
    showOnboarding,
    showSignup,
    currentLanguage,
    setCurrentLanguage,
    selectedDate,
    t,
    handleOnboardingComplete,
    handleSignupComplete,
    toggleLanguage,
  };
};
