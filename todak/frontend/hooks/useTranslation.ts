import { useState, useEffect, useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { resources } from '@/lib/i18n';

type Language = 'ko' | 'en';

export const useTranslation = () => {
  const [currentLanguage, setCurrentLanguage] = useState<Language>('ko');

  useEffect(() => {
    (async () => {
      const storedLanguage = await AsyncStorage.getItem('language');
      if (storedLanguage === 'ko' || storedLanguage === 'en') {
        setCurrentLanguage(storedLanguage as Language);
      }
    })();
  }, []);

  const toggleLanguage = useCallback(async () => {
    setCurrentLanguage(prev => {
      const newLang = prev === 'ko' ? 'en' : 'ko';
      AsyncStorage.setItem('language', newLang); // async but not awaited here
      return newLang;
    });
  }, []);

  const handleLanguageChange = async (lang: Language) => {
    await AsyncStorage.setItem('language', lang);
    setCurrentLanguage(lang);
  };

  const t = useCallback((key: string) => {
    const lang = currentLanguage || 'ko';
    const translations = resources[lang]?.translation as Record<string, string>;
    return translations[key] || key;
  }, [currentLanguage]);
  

  return {
    t,
    currentLanguage,
    toggleLanguage,
    setCurrentLanguage: handleLanguageChange,
  };
};
